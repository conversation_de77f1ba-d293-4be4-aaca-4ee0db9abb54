#!/usr/bin/env python3
"""
测试A股新闻分析模型的脚本
"""

import sys
import os
sys.path.append('TradingAgents-main')

from tradingagents.dataflows.interface import get_a_stock_fundamentals
from tradingagents.dataflows.tushare_utils import get_a_stock_data

def test_tushare_connection():
    """测试Tushare连接和Token是否有效"""
    print("=== 测试Tushare连接 ===")
    
    try:
        # 测试获取A股数据
        ticker = "000001"  # 平安银行
        start_date = "2024-01-01"
        end_date = "2024-01-10"
        
        print(f"正在获取 {ticker} 的市场数据...")
        result = get_a_stock_data(ticker, start_date, end_date)
        print("✅ 市场数据获取成功")
        print(f"数据预览:\n{result[:500]}...")
        
    except Exception as e:
        print(f"❌ 市场数据获取失败: {e}")
        return False
    
    try:
        # 测试获取基本面数据
        print(f"\n正在获取 {ticker} 的基本面数据...")
        curr_date = "2024-01-01"
        fundamentals = get_a_stock_fundamentals(ticker + ".SZ", curr_date)
        print("✅ 基本面数据获取成功")
        print(f"基本面数据预览:\n{fundamentals[:500]}...")
        
    except Exception as e:
        print(f"❌ 基本面数据获取失败: {e}")
        return False
    
    return True

def test_news_analyst_tools():
    """测试新闻分析器的工具配置"""
    print("\n=== 测试新闻分析器工具配置 ===")
    
    try:
        from tradingagents.agents.utils.agent_utils import Toolkit
        
        toolkit = Toolkit()
        
        # 检查是否有get_a_stock_fundamentals工具
        if hasattr(toolkit, 'get_a_stock_fundamentals'):
            print("✅ get_a_stock_fundamentals 工具已定义")
            
            # 测试工具调用
            result = toolkit.get_a_stock_fundamentals("000001.SZ", "2024-01-01")
            print("✅ A股基本面工具调用成功")
            print(f"工具返回结果预览:\n{result[:300]}...")
            
        else:
            print("❌ get_a_stock_fundamentals 工具未找到")
            return False
            
    except Exception as e:
        print(f"❌ 工具测试失败: {e}")
        return False
    
    return True

def test_news_analyst_integration():
    """测试新闻分析器的A股集成"""
    print("\n=== 测试新闻分析器A股集成 ===")
    
    try:
        from tradingagents.agents.analysts.news_analyst import create_news_analyst
        from tradingagents.agents.utils.agent_utils import Toolkit
        from langchain_openai import ChatOpenAI
        
        # 创建模拟的LLM和工具包
        llm = ChatOpenAI(model="gpt-3.5-turbo", temperature=0)
        toolkit = Toolkit()
        
        # 创建新闻分析器
        news_analyst = create_news_analyst(llm, toolkit)
        
        print("✅ 新闻分析器创建成功")
        
        # 检查分析器是否包含A股工具
        # 这里我们只是验证函数能够正常创建，实际调用需要完整的状态对象
        print("✅ 新闻分析器A股集成验证通过")
        
    except Exception as e:
        print(f"❌ 新闻分析器集成测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始测试A股新闻分析模型...")
    
    # 测试1: Tushare连接
    tushare_ok = test_tushare_connection()
    
    # 测试2: 工具配置
    tools_ok = test_news_analyst_tools()
    
    # 测试3: 新闻分析器集成
    integration_ok = test_news_analyst_integration()
    
    # 总结
    print("\n" + "="*50)
    print("测试结果总结:")
    print(f"Tushare连接: {'✅ 通过' if tushare_ok else '❌ 失败'}")
    print(f"工具配置: {'✅ 通过' if tools_ok else '❌ 失败'}")
    print(f"分析器集成: {'✅ 通过' if integration_ok else '❌ 失败'}")
    
    if all([tushare_ok, tools_ok, integration_ok]):
        print("\n🎉 所有测试通过！新闻分析模型可以用于A股分析。")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试。")
        return False

if __name__ == "__main__":
    main()
