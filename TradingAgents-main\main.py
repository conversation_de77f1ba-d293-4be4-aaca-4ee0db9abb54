import logging
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

if __name__ == "__main__":
    # Create a custom config
    config = DEFAULT_CONFIG.copy()
    config["llm_provider"] = "ollama"  # 使用支持中文的模型平台
    config["deep_think_llm"] = "qwen2"  # 使用通义千问处理中文金融术语
    config["quick_think_llm"] = "glm-4-flash"  # 使用GLM-4-Flash进行快速响应
    config["backend_url"] = "https://api.openai.com/v1"  # 根据所选平台调整
    config["max_debate_rounds"] = 3  # 增加辩论轮数，提高分析深度
    config["online_tools"] = True  # 启用在线工具以获取实时数据

    # Initialize with custom config
    ta = TradingAgentsGraph(debug=True, config=config)

    # Forward propagate
    _, decision = ta.propagate("603299", "2024-05-10")
    logging.info(f"Decision: {decision}")