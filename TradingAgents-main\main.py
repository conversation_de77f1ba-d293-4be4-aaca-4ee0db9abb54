import logging
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

if __name__ == "__main__":
    # 分析福耀玻璃600660
    config = DEFAULT_CONFIG.copy()
    config["max_debate_rounds"] = 3
    config["max_risk_discuss_rounds"] = 3
    config["online_tools"] = True

    print("🚀 开始分析福耀玻璃(600660)...")
    print(f"📊 使用智谱AI GLM-4模型")
    print(f"🔧 配置: {config['llm_provider']} - {config['deep_think_llm']}")

    # 选择所有分析师
    selected_analysts = ["market", "social", "news", "fundamentals"]

    # 初始化系统
    ta = TradingAgentsGraph(
        selected_analysts=selected_analysts,
        debug=True,
        config=config
    )

    # 分析600660福耀玻璃
    ticker = "600660.SH"
    analysis_date = "2024-12-19"  # 今天的日期

    print(f"📈 分析股票: {ticker} (福耀玻璃)")
    print(f"📅 分析日期: {analysis_date}")
    print("🔄 开始分析流程...")

    # 执行分析
    final_state, decision = ta.propagate(ticker, analysis_date)

    print("="*60)
    print("🎉 分析完成！")
    print("="*60)

    # 输出结果
    if final_state:
        print("📊 分析结果:")
        for key, value in final_state.items():
            if value and isinstance(value, str) and len(value) > 10:
                print(f"\n{key}:")
                print("-" * 40)
                print(value[:300] + "..." if len(value) > 300 else value)

    print(f"\n🎯 最终决策: {decision}")
    logging.info(f"600660分析完成 - 决策: {decision}")