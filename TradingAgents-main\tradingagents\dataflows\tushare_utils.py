import tushare as ts
import os
import pandas as pd
from datetime import datetime

def get_a_stock_data(ticker, start_date, end_date):
    """
    使用Tushare获取A股市场数据
    
    参数:
        ticker (str): A股股票代码，例如 '603299'
        start_date (str): 开始日期，格式为 'YYYY-MM-DD'
        end_date (str): 结束日期，格式为 'YYYY-MM-DD'
    
    返回:
        str: 包含指定时间段内股票数据的字符串表示
    注意:
        1. 需要有效的 Tushare Token 才能正常工作
        2. 请在使用前替换 'your_tushare_token' 为你自己的有效 Token
    """
    # 初始化Tushare API（需提前申请token）
    ts.set_token('fdee2a3b6461cb9c67abd321bb340f0f42c2ac16cfc362ed7a222a16')  # 使用你的Tushare Token
    pro = ts.pro_api()

    # 根据股票代码前缀判断市场，并添加对应的后缀
    if ticker.startswith(('5', '6')):  # 上海证券交易所
        ts_suffix = '.SH'
    elif ticker.startswith(('0', '3')):  # 深圳证券交易所
        ts_suffix = '.SZ'
    else:
        raise ValueError(f"Unknown market for ticker: {ticker}")
    
    # 获取历史行情数据
    df = pro.daily(ts_code=ticker + ts_suffix, start_date=start_date.replace('-', ''), end_date=end_date.replace('-', ''))
    if df.empty:
        raise ValueError(f"No data returned for {ticker}")
    
    # 数据排序并转换为字符串输出
    df.sort_values(by='trade_date', ascending=False, inplace=True)
    return df.to_string(index=False)