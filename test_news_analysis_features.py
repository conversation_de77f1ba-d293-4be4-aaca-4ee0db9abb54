#!/usr/bin/env python3
"""
测试新闻分析模型的各项功能
"""

import sys
import os
sys.path.append('TradingAgents-main')

def test_news_data_sources():
    """测试新闻数据源功能"""
    print("=== 测试新闻数据源功能 ===")
    
    # 测试1: Google新闻数据获取
    print("\n1. 测试Google新闻数据获取")
    try:
        from tradingagents.dataflows.googlenews_utils import getNewsData
        
        # 测试搜索苹果公司新闻
        query = "Apple AAPL"
        start_date = "2024-01-01"
        end_date = "2024-01-05"
        
        print(f"正在搜索: {query} ({start_date} 到 {end_date})")
        news_results = getNewsData(query, start_date, end_date)
        
        if news_results:
            print(f"✅ 成功获取 {len(news_results)} 条新闻")
            print("示例新闻:")
            for i, news in enumerate(news_results[:2]):
                print(f"  {i+1}. {news['title']}")
                print(f"     来源: {news['source']}")
                print(f"     摘要: {news['snippet'][:100]}...")
        else:
            print("⚠️ 未获取到新闻数据")
            
    except Exception as e:
        print(f"❌ Google新闻测试失败: {e}")
    
    # 测试2: 工具包中的新闻工具
    print("\n2. 测试工具包中的新闻工具")
    try:
        from tradingagents.agents.utils.agent_utils import Toolkit
        
        toolkit = Toolkit()
        
        # 检查可用的新闻相关工具
        news_tools = []
        for attr_name in dir(toolkit):
            if 'news' in attr_name.lower() or 'reddit' in attr_name.lower() or 'google' in attr_name.lower():
                news_tools.append(attr_name)
        
        print(f"发现 {len(news_tools)} 个新闻相关工具:")
        for tool in news_tools:
            print(f"  - {tool}")
            
        # 测试Google新闻工具
        if hasattr(toolkit, 'get_google_news'):
            print("\n测试Google新闻工具:")
            try:
                result = toolkit.get_google_news("AAPL", "2024-01-01")
                print("✅ Google新闻工具可用")
                print(f"返回结果长度: {len(result) if result else 0} 字符")
            except Exception as e:
                print(f"❌ Google新闻工具测试失败: {e}")
        
        # 测试Reddit新闻工具
        if hasattr(toolkit, 'get_reddit_news'):
            print("\n测试Reddit新闻工具:")
            try:
                result = toolkit.get_reddit_news("2024-01-01")
                print("✅ Reddit新闻工具可用")
                print(f"返回结果长度: {len(result) if result else 0} 字符")
            except Exception as e:
                print(f"❌ Reddit新闻工具测试失败: {e}")
                
    except Exception as e:
        print(f"❌ 工具包测试失败: {e}")

def test_news_analyst_configuration():
    """测试新闻分析器配置"""
    print("\n=== 测试新闻分析器配置 ===")
    
    try:
        from tradingagents.agents.analysts.news_analyst import create_news_analyst
        from tradingagents.agents.utils.agent_utils import Toolkit
        
        # 创建模拟的LLM和工具包
        class MockLLM:
            def bind_tools(self, tools):
                return self
            
            def invoke(self, messages):
                class MockResult:
                    def __init__(self):
                        self.tool_calls = []
                        self.content = "模拟新闻分析结果"
                return MockResult()
        
        llm = MockLLM()
        toolkit = Toolkit()
        
        # 创建新闻分析器
        news_analyst = create_news_analyst(llm, toolkit)
        print("✅ 新闻分析器创建成功")
        
        # 测试分析器状态处理
        mock_state = {
            "trade_date": "2024-01-01",
            "company_of_interest": "AAPL",
            "messages": []
        }
        
        try:
            result = news_analyst(mock_state)
            print("✅ 新闻分析器状态处理成功")
            print(f"返回键: {list(result.keys())}")
        except Exception as e:
            print(f"❌ 新闻分析器状态处理失败: {e}")
            
    except Exception as e:
        print(f"❌ 新闻分析器配置测试失败: {e}")

def test_tool_configuration_issues():
    """检查工具配置问题"""
    print("\n=== 检查工具配置问题 ===")
    
    try:
        from tradingagents.agents.utils.agent_utils import Toolkit
        
        toolkit = Toolkit()
        
        # 检查新闻分析器中引用的工具是否存在
        required_tools = [
            'get_stock_news_openai',
            'get_global_news_openai', 
            'get_googlenews_data',  # 这个可能不存在
            'get_reddit_data',      # 这个可能不存在
            'get_a_stock_fundamentals'
        ]
        
        print("检查必需工具:")
        missing_tools = []
        available_tools = []
        
        for tool in required_tools:
            if hasattr(toolkit, tool):
                available_tools.append(tool)
                print(f"✅ {tool} - 可用")
            else:
                missing_tools.append(tool)
                print(f"❌ {tool} - 缺失")
        
        # 检查可能的替代工具
        print("\n检查可能的替代工具:")
        alternative_tools = {
            'get_googlenews_data': ['get_google_news'],
            'get_reddit_data': ['get_reddit_news', 'get_reddit_stock_info']
        }
        
        for missing_tool in missing_tools:
            if missing_tool in alternative_tools:
                alternatives = alternative_tools[missing_tool]
                found_alternatives = []
                for alt in alternatives:
                    if hasattr(toolkit, alt):
                        found_alternatives.append(alt)
                
                if found_alternatives:
                    print(f"  {missing_tool} 的替代工具: {found_alternatives}")
                else:
                    print(f"  {missing_tool} 无可用替代工具")
        
        return missing_tools, available_tools
        
    except Exception as e:
        print(f"❌ 工具配置检查失败: {e}")
        return [], []

def test_model_analysis_capabilities():
    """测试模型分析能力"""
    print("\n=== 测试模型分析能力 ===")
    
    try:
        from tradingagents.graph.trading_graph import TradingAgentsGraph
        
        # 创建交易图实例
        print("创建交易分析图...")
        config = {
            "llm_provider": "openai",  # 可能需要配置
            "online_tools": False,     # 使用离线工具避免API依赖
        }
        
        try:
            graph = TradingAgentsGraph(
                selected_analysts=["news"],  # 只测试新闻分析
                debug=True,
                config=config
            )
            print("✅ 交易分析图创建成功")
            
            # 检查图的组件
            print("检查图组件:")
            if hasattr(graph, 'toolkit'):
                print("  ✅ 工具包已配置")
            if hasattr(graph, 'graph'):
                print("  ✅ 分析图已构建")
            
        except Exception as e:
            print(f"❌ 交易分析图创建失败: {e}")
            print("这可能是由于缺少LLM配置或其他依赖")
            
    except Exception as e:
        print(f"❌ 模型分析能力测试失败: {e}")

def suggest_fixes(missing_tools):
    """建议修复方案"""
    print("\n=== 修复建议 ===")
    
    if missing_tools:
        print("发现以下问题需要修复:")
        
        for tool in missing_tools:
            if tool == 'get_googlenews_data':
                print(f"\n{tool}:")
                print("  问题: 新闻分析器引用了不存在的工具")
                print("  修复: 将 get_googlenews_data 改为 get_google_news")
                
            elif tool == 'get_reddit_data':
                print(f"\n{tool}:")
                print("  问题: 新闻分析器引用了不存在的工具")
                print("  修复: 将 get_reddit_data 改为 get_reddit_news")
                
            elif tool == 'get_a_stock_fundamentals':
                print(f"\n{tool}:")
                print("  问题: A股基本面工具可能未正确定义")
                print("  修复: 确保在Toolkit类中添加此工具的@tool装饰器定义")
    
    print("\n通用建议:")
    print("1. 修复工具名称不匹配问题")
    print("2. 确保所有引用的工具都在Toolkit类中正确定义")
    print("3. 测试新闻数据获取功能的网络连接")
    print("4. 配置适当的LLM提供商以进行完整测试")

def main():
    """主测试函数"""
    print("开始测试新闻分析模型功能...")
    print("="*60)
    
    # 测试1: 新闻数据源
    test_news_data_sources()
    
    # 测试2: 新闻分析器配置
    test_news_analyst_configuration()
    
    # 测试3: 工具配置问题
    missing_tools, available_tools = test_tool_configuration_issues()
    
    # 测试4: 模型分析能力
    test_model_analysis_capabilities()
    
    # 提供修复建议
    suggest_fixes(missing_tools)
    
    # 总结
    print("\n" + "="*60)
    print("测试总结:")
    print(f"可用工具: {len(available_tools)}")
    print(f"缺失工具: {len(missing_tools)}")
    
    if len(missing_tools) == 0:
        print("🎉 所有工具配置正确，新闻分析功能应该可以正常工作！")
    else:
        print("⚠️ 发现配置问题，需要修复后才能完全正常工作")
        print("主要问题: 工具名称不匹配")

if __name__ == "__main__":
    main()
