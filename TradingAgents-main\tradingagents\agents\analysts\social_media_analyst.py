from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
import time
import json


def create_social_media_analyst(llm, toolkit):
    def social_media_analyst_node(state):
        current_date = state["trade_date"]
        ticker = state["company_of_interest"]
        
        if toolkit.config["online_tools"]:
            tools = [toolkit.get_reddit_stock_info, toolkit.get_stock_news_openai, toolkit.get_a_stock_fundamentals]  # 添加A股社交媒体数据接口
        else:
            tools = [toolkit.get_reddit_news, toolkit.get_reddit_stock_info, toolkit.get_a_stock_fundamentals]  # 本地A股社交媒体数据接口
        
        system_message = (
            "你是一个专注于分析社交媒体情绪的研究员。你的任务是通过分析Reddit、Twitter等平台上的讨论，提供全面的市场情绪报告，以帮助交易员做出决策。"
            + " 请务必在报告末尾附上一个Markdown表格来组织关键点，确保内容清晰易读。",
        )

        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    "你是一个专业的AI助手，与其他分析师协作进行社交媒体情绪分析。"
                    "使用提供的工具来分析雪球、微博等平台上的投资者情绪和讨论。"
                    "如果无法完全回答，没关系，其他具有不同工具的助手会继续你的工作。"
                    "如果你或其他助手得出最终交易建议：**买入/持有/卖出**，"
                    "请在回复前加上'最终交易建议：**买入/持有/卖出**'，让团队知道可以停止分析。"
                    "你可以使用以下工具：{tool_names}。\n{system_message}"
                    "参考信息：当前日期是{current_date}，我们要分析的公司是{ticker}",
                ),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

        prompt = prompt.partial(system_message=system_message)
        prompt = prompt.partial(tool_names=", ".join([tool.name for tool in tools]))
        prompt = prompt.partial(current_date=current_date)
        prompt = prompt.partial(ticker=ticker)

        chain = prompt | llm.bind_tools(tools)

        result = chain.invoke(state["messages"])

        report = ""

        if len(result.tool_calls) == 0:
            report = result.content

        return {
            "messages": [result],
            "sentiment_report": report,
        }

    return social_media_analyst_node
