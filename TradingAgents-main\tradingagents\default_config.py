import os

DEFAULT_CONFIG = {
    "project_dir": os.path.abspath(os.path.join(os.path.dirname(__file__), ".")),
    "results_dir": os.getenv("TRADINGAGENTS_RESULTS_DIR", "./results"),
    "data_dir": "d:/vscode_work/TradingAgents/data",
    "data_cache_dir": os.path.join(
        os.path.abspath(os.path.join(os.path.dirname(__file__), ".")),
        "dataflows/data_cache",
    ),
    # LLM settings - 使用国内服务
    "llm_provider": "zhipu",  # 智谱AI
    "deep_think_llm": "glm-4",
    "quick_think_llm": "glm-4-flash",
    "backend_url": "https://open.bigmodel.cn/api/paas/v4/",
    "zhipu_api_key": "911b26b84b024bdf9a9bf0f3dfdc8475.ef32nQybus1JXCVw",
    # 国内新闻源配置
    "news_sources": {
        "primary": "eastmoney",  # 东方财富
        "secondary": "sina",     # 新浪财经
        "social": "xueqiu"       # 雪球
    },
    # Debate and discussion settings
    "max_debate_rounds": 1,
    "max_risk_discuss_rounds": 1,
    "max_recur_limit": 100,
    # Tool settings
    "online_tools": True,
}
