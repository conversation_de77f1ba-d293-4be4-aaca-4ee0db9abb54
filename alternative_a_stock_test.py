#!/usr/bin/env python3
"""
使用替代数据源的A股分析测试
"""

import tushare as ts
import yfinance as yf

def test_tushare_free_api():
    """测试Tushare免费API"""
    print("=== 测试Tushare免费API ===")
    
    try:
        # 测试免费的股票基本信息接口
        print("正在测试免费股票基本信息...")
        df = ts.get_stock_basics()
        if df is not None and not df.empty:
            print(f"✅ 获取到 {len(df)} 只股票的基本信息")
            print("前5只股票信息:")
            print(df.head())
            return True
        else:
            print("⚠️ 免费API返回空数据")
            return False
            
    except Exception as e:
        print(f"❌ Tushare免费API测试失败: {e}")
        return False

def test_yfinance_a_stock():
    """测试使用yfinance获取A股数据"""
    print("\n=== 测试yfinance获取A股数据 ===")
    
    try:
        # 测试获取A股数据（使用港股通或者美股ADR）
        # 平安银行的港股代码
        ticker = "1318.HK"  # 平安银行港股
        print(f"正在测试获取 {ticker} 数据...")
        
        stock = yf.Ticker(ticker)
        hist = stock.history(period="1mo")
        
        if not hist.empty:
            print(f"✅ 获取到 {len(hist)} 条历史数据")
            print("最近5天数据:")
            print(hist.tail())
            
            # 获取股票信息
            info = stock.info
            print(f"\n股票信息:")
            print(f"公司名称: {info.get('longName', 'N/A')}")
            print(f"行业: {info.get('industry', 'N/A')}")
            print(f"市值: {info.get('marketCap', 'N/A')}")
            
            return True
        else:
            print("⚠️ 未获取到历史数据")
            return False
            
    except Exception as e:
        print(f"❌ yfinance A股测试失败: {e}")
        return False

def test_alternative_a_stock_analysis():
    """测试替代的A股分析方案"""
    print("\n=== 测试替代A股分析方案 ===")
    
    try:
        # 模拟A股基本面分析函数
        def get_a_stock_fundamentals_alternative(ticker, curr_date):
            """
            替代的A股基本面获取函数
            使用yfinance获取港股或ADR数据作为参考
            """
            
            # A股代码到港股/ADR的映射
            a_to_hk_mapping = {
                "000001": "1318.HK",  # 平安银行
                "000002": "0001.HK",  # 万科A -> 万科企业
                "600036": "3968.HK",  # 招商银行
                "600519": "N/A",      # 茅台（无对应港股）
            }
            
            # 去掉.SZ或.SH后缀
            clean_ticker = ticker.replace('.SZ', '').replace('.SH', '')
            
            if clean_ticker in a_to_hk_mapping:
                hk_ticker = a_to_hk_mapping[clean_ticker]
                if hk_ticker != "N/A":
                    try:
                        stock = yf.Ticker(hk_ticker)
                        info = stock.info
                        hist = stock.history(period="1mo")
                        
                        result = f"""## {ticker} 公司基本面信息（基于港股数据 {hk_ticker}）

### 公司基本信息
公司名称: {info.get('longName', 'N/A')}
行业: {info.get('industry', 'N/A')}
板块: {info.get('sector', 'N/A')}
市值: {info.get('marketCap', 'N/A')}
员工数: {info.get('fullTimeEmployees', 'N/A')}

### 财务指标
市盈率: {info.get('trailingPE', 'N/A')}
市净率: {info.get('priceToBook', 'N/A')}
股息率: {info.get('dividendYield', 'N/A')}
ROE: {info.get('returnOnEquity', 'N/A')}

### 最近价格表现
当前价格: {info.get('currentPrice', 'N/A')} {info.get('currency', '')}
52周最高: {info.get('fiftyTwoWeekHigh', 'N/A')}
52周最低: {info.get('fiftyTwoWeekLow', 'N/A')}

注：数据来源于对应港股，仅供参考
"""
                        return result
                    except:
                        return f"无法获取 {ticker} 对应的港股数据 {hk_ticker}"
                else:
                    return f"{ticker} 暂无对应的港股数据源"
            else:
                return f"暂不支持 {ticker} 的数据获取"
        
        # 测试函数
        test_ticker = "000001.SZ"
        result = get_a_stock_fundamentals_alternative(test_ticker, "2024-01-01")
        print("✅ 替代A股分析方案测试成功")
        print("分析结果:")
        print(result)
        
        return True
        
    except Exception as e:
        print(f"❌ 替代A股分析方案测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试替代A股数据获取方案...")
    
    # 测试1: Tushare免费API
    tushare_free_ok = test_tushare_free_api()
    
    # 测试2: yfinance A股相关数据
    yfinance_ok = test_yfinance_a_stock()
    
    # 测试3: 替代分析方案
    alternative_ok = test_alternative_a_stock_analysis()
    
    # 总结
    print("\n" + "="*60)
    print("测试结果总结:")
    print(f"Tushare免费API: {'✅ 可用' if tushare_free_ok else '❌ 不可用'}")
    print(f"yfinance港股数据: {'✅ 可用' if yfinance_ok else '❌ 不可用'}")
    print(f"替代分析方案: {'✅ 可用' if alternative_ok else '❌ 不可用'}")
    
    print("\n📋 A股分析模型适用性最终评估:")
    
    if any([tushare_free_ok, yfinance_ok, alternative_ok]):
        print("✅ 新闻分析模型可以用于A股分析")
        print("\n🎯 可用的数据源方案:")
        
        if tushare_free_ok:
            print("1. ✅ Tushare免费API - 可获取基本股票信息")
        
        if yfinance_ok:
            print("2. ✅ yfinance港股数据 - 可获取A股对应港股的详细信息")
        
        if alternative_ok:
            print("3. ✅ 替代分析方案 - 通过港股/ADR数据间接分析A股")
        
        print("\n📝 使用建议:")
        print("• 新闻分析模型的框架完全支持A股分析")
        print("• 可以使用yfinance获取A股对应港股的基本面数据")
        print("• 新闻数据可以通过OpenAI API或Google新闻获取")
        print("• 建议结合多个数据源提高分析准确性")
        
        print("\n⚙️ 配置建议:")
        print("• 修改get_a_stock_fundamentals函数使用yfinance作为备选")
        print("• 建立A股到港股的代码映射表")
        print("• 配置中文新闻源以获取更准确的A股新闻")
        
        return True
    else:
        print("❌ 当前配置下无法有效获取A股数据")
        print("建议：")
        print("1. 申请Tushare专业版权限")
        print("2. 或使用其他A股数据API（如聚宽、米筐等）")
        return False

if __name__ == "__main__":
    main()
